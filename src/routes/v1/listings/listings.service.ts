import { HTTPException } from "hono/http-exception";
import { eq, and, sql, desc, asc, like, or, count } from "drizzle-orm";
import db from "@/db";
import { listings, listingDetails, listingStatusHistory, userProfiles } from "@/db/schema";
import { CsvParser, type ParsedCsvData } from "@/lib/csv-parser";
import { createListingRequestSchema } from "./listings.routes";
import type { z } from "zod";

// Use the generated schema types instead of manual interfaces
export type CreateListingData = z.infer<typeof createListingRequestSchema> & {
  workspaceId: string;
  createdBy: string;
};

export type UpdateListingData = Partial<CreateListingData>;

export interface ListingDetailsData {
  business_description?: string;
  brief_description?: string;
  financial_details?: {
    revenue_2023?: number;
    ebitda?: number;
    assets_included?: string[];
    inventory_value?: number;
    additional_financial_info?: any;
  };
  operations?: {
    business_model?: string;
    key_features?: string[];
    competitive_advantages?: string[];
    operational_details?: any;
  };
  growth_opportunities?: string[];
  reason_for_sale?: string;
  training_period?: string;
  support_type?: string;
  financing_available?: boolean;
  equipment_highlights?: string[];
  supplier_relationships?: string;
  real_estate_status?: string;
  lease_details?: {
    lease_terms?: string;
    monthly_rent?: number;
    lease_expiration?: string;
    renewal_options?: string;
    landlord_info?: any;
  };
}

export interface ListingFilters {
  page?: number;
  limit?: number;
  status?: string;
  industry?: string;
  assignedTo?: string;
  minPrice?: number;
  maxPrice?: number;
  location?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

export interface StatusChangeData {
  status: string;
  reason?: string;
  notes?: string;
  changedBy: string;
  workspaceId: string;
}

export class ListingsService {
  
  // Valid status values that match the database constraint
  private static readonly VALID_STATUS_VALUES = [
    'draft',
    'active', 
    'pending',
    'sold',
    'withdrawn'
  ] as const;

  /**
   * Validate status value against allowed values
   */
  private static validateStatus(status?: string | null): string {
    if (!status) return 'draft'; // default status
    
    const normalizedStatus = status.toLowerCase().trim();
    
    // Handle common variations to match actual DB constraint
    const statusMap: Record<string, string> = {
      'under contract': 'pending',
      'undercontract': 'pending',
      'under_contract': 'pending',
      'pending': 'pending',
      'closed': 'sold',
      'available': 'active',
      'listed': 'active',
      'off market': 'withdrawn',
      'off-market': 'withdrawn',
      'offmarket': 'withdrawn',
      'confidential': 'active', // Map confidential to active since DB doesn't have it
      'expired': 'withdrawn',  // Map expired to withdrawn
    };
    
    const mappedStatus = statusMap[normalizedStatus] || normalizedStatus;
    
    if (this.VALID_STATUS_VALUES.includes(mappedStatus as any)) {
      return mappedStatus;
    }
    
    // If invalid status, default to draft
    console.warn(`Invalid status value "${status}" provided, defaulting to "draft"`);
    return 'draft';
  }

  /**
   * Calculate days listed for a listing based on date_listed
   */
  private static calculateDaysListed(dateListed?: string | null): number | null {
    if (!dateListed) return null;

    const listedDate = new Date(dateListed);
    const today = new Date();
    const diffTime = today.getTime() - listedDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays >= 0 ? diffDays : 0;
  }

  /**
   * Auto-update days_listed field for all active listings
   */
  static async updateDaysListedBatch() {
    const activeListings = await db
      .select({ id: listings.id, dateListed: listings.dateListed })
      .from(listings)
      .where(
        and(
          eq(listings.status, 'active'),
          sql`${listings.dateListed} IS NOT NULL`
        )
      );

    for (const listing of activeListings) {
      const daysListed = this.calculateDaysListed(listing.dateListed);
      if (daysListed !== null) {
        await db
          .update(listings)
          .set({
            daysListed,
            updatedAt: sql`now()`
          })
          .where(eq(listings.id, listing.id));
      }
    }
  }

  /**
   * Get all listings with filtering, sorting and pagination
   */
  static async getListings(filters: ListingFilters, workspaceId: string) {
    const {
      page = 1,
      limit = 20,
      status,
      industry,
      assignedTo,
      minPrice,
      maxPrice,
      location,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search
    } = filters;

    // Build where conditions
    const conditions = [eq(listings.workspaceId, workspaceId)];

    if (status) {
      conditions.push(eq(listings.status, status));
    }

    if (industry) {
      conditions.push(eq(listings.industry, industry));
    }

    if (assignedTo) {
      conditions.push(eq(listings.assignedTo, assignedTo));
    }

    if (minPrice !== undefined && maxPrice !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric BETWEEN ${minPrice} AND ${maxPrice}`);
    } else if (minPrice !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric >= ${minPrice}`);
    } else if (maxPrice !== undefined) {
      conditions.push(sql`${listings.askingPrice}::numeric <= ${maxPrice}`);
    }

    if (location) {
      conditions.push(like(listings.generalLocation, `%${location}%`));
    }

    if (search) {
      conditions.push(
        or(
          like(listings.businessName, `%${search}%`),
          like(listings.industry, `%${search}%`),
          like(listings.description, `%${search}%`),
          like(listings.generalLocation, `%${search}%`)
        )!
      );
    }

    // Apply sorting
    const sortColumn = sortBy === 'asking_price' ? sql`${listings.askingPrice}::numeric` :
      sortBy === 'business_name' ? listings.businessName :
        sortBy === 'date_listed' ? listings.dateListed :
          sortBy === 'days_listed' ? listings.daysListed :
            sortBy === 'updated_at' ? listings.updatedAt :
              listings.createdAt;

    const orderByClause = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn);

    // Get total count for pagination
    const [{ total }] = await db
      .select({ total: count() })
      .from(listings)
      .where(and(...conditions));

    // Apply pagination and get results
    const offset = (page - 1) * limit;
    const results = await db
      .select({
        id: listings.id,
        workspaceId: listings.workspaceId,
        createdBy: listings.createdBy,
        assignedTo: listings.assignedTo,
        businessName: listings.businessName,
        industry: listings.industry,
        askingPrice: listings.askingPrice,
        cashFlowSde: listings.cashFlowSde,
        annualRevenue: listings.annualRevenue,
        status: listings.status,
        generalLocation: listings.generalLocation,
        yearEstablished: listings.yearEstablished,
        employees: listings.employees,
        ownerHoursWeek: listings.ownerHoursWeek,
        dateListed: listings.dateListed,
        daysListed: listings.daysListed,
        title: listings.title,
        description: listings.description,
        price: listings.price,
        teamVisibility: listings.teamVisibility,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
      })
      .from(listings)
      .where(and(...conditions))
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get a single listing by ID with optional details
   */
  static async getListingById(id: string, workspaceId: string, includeDetails = true) {
    const listing = await db
      .select()
      .from(listings)
      .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
      .limit(1);

    if (listing.length === 0) {
      throw new HTTPException(404, { message: "Listing not found" });
    }

    let details = null;
    if (includeDetails) {
      const detailsResult = await db
        .select()
        .from(listingDetails)
        .where(eq(listingDetails.listingId, id))
        .limit(1);

      details = detailsResult[0] || null;
    }

    return {
      ...listing[0],
      details,
    };
  }

  /**
   * Create a new listing
   */
  static async createListing(data: CreateListingData): Promise<any> {
    const now = new Date().toISOString();

    // Calculate days_listed if dateListed is provided
    const daysListed = data.dateListed ? this.calculateDaysListed(data.dateListed) : null;

    return await db.transaction(async (tx) => {
      // Create the main listing record
      const [listing] = await tx
        .insert(listings)
        .values({
          workspaceId: data.workspaceId,
          createdBy: data.createdBy,
          assignedTo: data.assignedTo || null,
          businessName: data.businessName,
          industry: data.industry,
          askingPrice: data.askingPrice?.toString(),
          cashFlowSde: data.cashFlowSde?.toString(),
          annualRevenue: data.annualRevenue?.toString(),
          status: this.validateStatus(data.status),
          generalLocation: data.generalLocation,
          yearEstablished: data.yearEstablished,
          employees: data.employees,
          ownerHoursWeek: data.ownerHoursWeek,
          dateListed: data.dateListed,
          daysListed: daysListed,
          // Legacy fields
          title: data.title || data.businessName,
          description: data.description,
          price: data.askingPrice?.toString(), // Copy to legacy field
          teamVisibility: data.teamVisibility || 'all',
          createdAt: now,
          updatedAt: now,
        })
        .returning();

      // Create listing details if provided
      let details = null;
      if (data.details) {
        [details] = await tx
          .insert(listingDetails)
          .values({
            listingId: listing.id,
            businessDescription: data.details.businessDescription,
            briefDescription: data.details.briefDescription,
            financialDetails: data.details.financialDetails || {},
            operations: data.details.operations || {},
            growthOpportunities: data.details.growthOpportunities || [],
            reasonForSale: data.details.reasonForSale,
            trainingPeriod: data.details.trainingPeriod,
            supportType: data.details.supportType,
            financingAvailable: data.details.financingAvailable || false,
            equipmentHighlights: data.details.equipmentHighlights || [],
            supplierRelationships: data.details.supplierRelationships,
            realEstateStatus: data.details.realEstateStatus,
            leaseDetails: data.details.leaseDetails || {},
            createdAt: now,
            updatedAt: now,
          })
          .returning();
      }

      // Log initial status
      await tx
        .insert(listingStatusHistory)
        .values({
          listingId: listing.id,
          workspaceId: data.workspaceId,
          changedBy: data.createdBy,
          fromStatus: null,
          toStatus: data.status || 'draft',
          reason: 'Initial listing creation',
          createdAt: now,
        });

      return {
        ...listing,
        details,
      };
    });
  }

  /**
   * Update an existing listing
   */
  static async updateListing(id: string, data: UpdateListingData, workspaceId: string): Promise<any> {
    const now = new Date().toISOString();

    // Check if listing exists
    const existingListing = await this.getListingById(id, workspaceId, false);

    // Calculate days_listed if dateListed is being updated
    const daysListed = data.dateListed ? this.calculateDaysListed(data.dateListed) : undefined;

    return await db.transaction(async (tx) => {
      const updateData: any = {
        updatedAt: now,
      };

      // Update core fields
      if (data.businessName !== undefined) updateData.businessName = data.businessName;
      if (data.industry !== undefined) updateData.industry = data.industry;
      if (data.askingPrice !== undefined) {
        updateData.askingPrice = data.askingPrice?.toString();
        updateData.price = data.askingPrice?.toString(); // Update legacy field
      }
      if (data.cashFlowSde !== undefined) updateData.cashFlowSde = data.cashFlowSde?.toString();
      if (data.annualRevenue !== undefined) updateData.annualRevenue = data.annualRevenue?.toString();
      if (data.generalLocation !== undefined) updateData.generalLocation = data.generalLocation;
      if (data.yearEstablished !== undefined) updateData.yearEstablished = data.yearEstablished;
      if (data.employees !== undefined) updateData.employees = data.employees;
      if (data.ownerHoursWeek !== undefined) updateData.ownerHoursWeek = data.ownerHoursWeek;
      if (data.dateListed !== undefined) {
        updateData.dateListed = data.dateListed;
        updateData.daysListed = daysListed;
      }
      if (data.assignedTo !== undefined) updateData.assignedTo = data.assignedTo;
      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.teamVisibility !== undefined) updateData.teamVisibility = data.teamVisibility;

      // Update main listing
      const [updatedListing] = await tx
        .update(listings)
        .set(updateData)
        .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
        .returning();

      // Update or create details if provided
      let details = null;
      if (data.details) {
        const existingDetails = await tx
          .select()
          .from(listingDetails)
          .where(eq(listingDetails.listingId, id))
          .limit(1);

        if (existingDetails.length > 0) {
          // Update existing details
          [details] = await tx
            .update(listingDetails)
            .set({
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails,
              operations: data.details.operations,
              growthOpportunities: data.details.growthOpportunities,
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable,
              equipmentHighlights: data.details.equipmentHighlights,
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails,
              updatedAt: now,
            })
            .where(eq(listingDetails.listingId, id))
            .returning();
        } else {
          // Create new details
          [details] = await tx
            .insert(listingDetails)
            .values({
              listingId: id,
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails || {},
              operations: data.details.operations || {},
              growthOpportunities: data.details.growthOpportunities || [],
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable || false,
              equipmentHighlights: data.details.equipmentHighlights || [],
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails || {},
              createdAt: now,
              updatedAt: now,
            })
            .returning();
        }
      }

      return {
        ...updatedListing,
        details,
      };
    });
  }

  /**
   * Update an existing listing with status tracking
   * Combines listing update with status history tracking when status changes
   */
  static async updateListingWithStatus(id: string, data: UpdateListingData & { reason?: string; notes?: string }, workspaceId: string, userId: string): Promise<any> {
    const now = new Date().toISOString();

    // Check if listing exists
    const existingListing = await this.getListingById(id, workspaceId, false);

    // Calculate days_listed if dateListed is being updated
    const daysListed = data.dateListed ? this.calculateDaysListed(data.dateListed) : undefined;

    return await db.transaction(async (tx) => {
      const updateData: any = {
        updatedAt: now,
      };

      // Track if status is changing for history logging
      const isStatusChanging = data.status !== undefined && data.status !== existingListing.status;
      const fromStatus = existingListing.status;

      // Update core fields
      if (data.businessName !== undefined) updateData.businessName = data.businessName;
      if (data.industry !== undefined) updateData.industry = data.industry;
      if (data.askingPrice !== undefined) {
        updateData.askingPrice = data.askingPrice?.toString();
        updateData.price = data.askingPrice?.toString(); // Update legacy field
      }
      if (data.cashFlowSde !== undefined) updateData.cashFlowSde = data.cashFlowSde?.toString();
      if (data.annualRevenue !== undefined) updateData.annualRevenue = data.annualRevenue?.toString();
      if (data.generalLocation !== undefined) updateData.generalLocation = data.generalLocation;
      if (data.yearEstablished !== undefined) updateData.yearEstablished = data.yearEstablished;
      if (data.employees !== undefined) updateData.employees = data.employees;
      if (data.ownerHoursWeek !== undefined) updateData.ownerHoursWeek = data.ownerHoursWeek;
      if (data.dateListed !== undefined) {
        updateData.dateListed = data.dateListed;
        updateData.daysListed = daysListed;
      }
      if (data.assignedTo !== undefined) updateData.assignedTo = data.assignedTo;
      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.teamVisibility !== undefined) updateData.teamVisibility = data.teamVisibility;
      
      // Handle status update with validation
      if (data.status !== undefined) {
        updateData.status = this.validateStatus(data.status);
      }

      // Update main listing
      const [updatedListing] = await tx
        .update(listings)
        .set(updateData)
        .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
        .returning();

      // Log status change if status was modified
      let statusChange = null;
      if (isStatusChanging) {
        [statusChange] = await tx
          .insert(listingStatusHistory)
          .values({
            listingId: id,
            workspaceId: workspaceId,
            changedBy: userId,
            fromStatus: fromStatus,
            toStatus: updateData.status || data.status,
            reason: data.reason || 'Status updated via listing update',
            notes: data.notes,
            createdAt: now,
          })
          .returning();
      }

      // Update or create details if provided
      let details = null;
      if (data.details) {
        const existingDetails = await tx
          .select()
          .from(listingDetails)
          .where(eq(listingDetails.listingId, id))
          .limit(1);

        if (existingDetails.length > 0) {
          // Update existing details
          [details] = await tx
            .update(listingDetails)
            .set({
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails,
              operations: data.details.operations,
              growthOpportunities: data.details.growthOpportunities,
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable,
              equipmentHighlights: data.details.equipmentHighlights,
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails,
              updatedAt: now,
            })
            .where(eq(listingDetails.listingId, id))
            .returning();
        } else {
          // Create new details
          [details] = await tx
            .insert(listingDetails)
            .values({
              listingId: id,
              businessDescription: data.details.businessDescription,
              briefDescription: data.details.briefDescription,
              financialDetails: data.details.financialDetails || {},
              operations: data.details.operations || {},
              growthOpportunities: data.details.growthOpportunities || [],
              reasonForSale: data.details.reasonForSale,
              trainingPeriod: data.details.trainingPeriod,
              supportType: data.details.supportType,
              financingAvailable: data.details.financingAvailable || false,
              equipmentHighlights: data.details.equipmentHighlights || [],
              supplierRelationships: data.details.supplierRelationships,
              realEstateStatus: data.details.realEstateStatus,
              leaseDetails: data.details.leaseDetails || {},
              createdAt: now,
              updatedAt: now,
            })
            .returning();
        }
      }

      // Return response format that matches expectations
      const response = {
        ...updatedListing,
        details,
      };

      // If status changed, include status change info like the old updateListingStatus method
      if (statusChange) {
        return {
          listing: response,
          status_change: statusChange,
        };
      }

      return response;
    });
  }

  /**
   * Update listing status with history tracking
   */
  static async updateListingStatus(id: string, statusData: StatusChangeData): Promise<any> {
    const now = new Date().toISOString();

    return await db.transaction(async (tx) => {
      // Get current listing
      const [currentListing] = await tx
        .select()
        .from(listings)
        .where(and(eq(listings.id, id), eq(listings.workspaceId, statusData.workspaceId)))
        .limit(1);

      if (!currentListing) {
        throw new HTTPException(404, { message: "Listing not found" });
      }

      const fromStatus = currentListing.status;

      // Update listing status
      const [updatedListing] = await tx
        .update(listings)
        .set({
          status: statusData.status,
          updatedAt: now,
        })
        .where(eq(listings.id, id))
        .returning();

      // Log status change
      const [statusChange] = await tx
        .insert(listingStatusHistory)
        .values({
          listingId: id,
          workspaceId: statusData.workspaceId,
          changedBy: statusData.changedBy,
          fromStatus: fromStatus,
          toStatus: statusData.status,
          reason: statusData.reason,
          notes: statusData.notes,
          createdAt: now,
        })
        .returning();

      return {
        listing: updatedListing,
        status_change: statusChange,
      };
    });
  }

  /**
   * Delete a listing
   */
  static async deleteListing(id: string, workspaceId: string): Promise<void> {
    const result = await db
      .delete(listings)
      .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
      .execute();

    // Check if any rows were affected by checking if we can find the listing
    const check = await db
      .select({ id: listings.id })
      .from(listings)
      .where(and(eq(listings.id, id), eq(listings.workspaceId, workspaceId)))
      .limit(1);

    if (check.length > 0) {
      throw new HTTPException(500, { message: "Failed to delete listing" });
    }
  }

  /**
   * Bulk create listings from CSV import - HIGH-LATENCY OPTIMIZED VERSION
   * Optimized for remote databases with high connection latency
   */
  static async bulkCreateListings(listingsData: CreateListingData[]): Promise<any> {
    // For high-latency databases, use larger batches to minimize round trips
    const BATCH_SIZE = Math.min(1000, listingsData.length); // Use larger batches
    
    // Always use single batch for small datasets to minimize connection overhead
    if (listingsData.length <= BATCH_SIZE) {
      return await this.processSingleBatch(listingsData);
    }

    // For larger datasets, still batch but with larger sizes
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    for (let i = 0; i < listingsData.length; i += BATCH_SIZE) {
      const batch = listingsData.slice(i, i + BATCH_SIZE);
      const batchResults = await this.processBatch(batch, i);
      
      results.created.push(...batchResults.created);
      results.failed.push(...batchResults.failed);
    }

    return results;
  }

  /**
   * Process single batch without validation overhead for small datasets
   */
  private static async processSingleBatch(listingsData: CreateListingData[]): Promise<any> {
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    try {
      // Quick validation - fail fast
      for (let i = 0; i < listingsData.length; i++) {
        const data = listingsData[i];
        if (!data.businessName || !data.industry) {
          results.failed.push({
            index: i,
            error: 'Missing required fields: businessName or industry',
            data: data,
          });
          listingsData.splice(i, 1);
          i--; // Adjust index after splice
        }
      }

      // Bulk insert all valid records
      if (listingsData.length > 0) {
        const batchResult = await this.bulkInsertListings(listingsData);
        for (let i = 0; i < batchResult.count; i++) {
          results.created.push({ success: true });
        }
      }
    } catch (error) {
      // If anything fails, mark all remaining as failed
      listingsData.forEach((data, index) => {
        results.failed.push({
          index,
          error: error instanceof Error ? error.message : 'Bulk insert failed',
          data: data,
        });
      });
    }

    return results;
  }

  /**
   * Process a batch of listings with optimized bulk operations
   */
  private static async processBatch(batch: CreateListingData[], startIndex: number): Promise<any> {
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    // Pre-validate all records in the batch before any database operations
    const validRecords: CreateListingData[] = [];
    const invalidRecords: any[] = [];

    for (let i = 0; i < batch.length; i++) {
      try {
        const data = batch[i];
        // Basic validation
        if (!data.businessName || !data.industry) {
          throw new Error('Missing required fields: businessName or industry');
        }
        validRecords.push(data);
      } catch (error) {
        invalidRecords.push({
          index: startIndex + i,
          error: error instanceof Error ? error.message : 'Validation error',
          data: batch[i],
        });
      }
    }

    // Add failed validations to results
    results.failed.push(...invalidRecords);

    // Process valid records in bulk
    if (validRecords.length > 0) {
      const batchResult = await this.bulkInsertListings(validRecords);
      // Add the count to created results (without detailed records)
      for (let i = 0; i < batchResult.count; i++) {
        results.created.push({ success: true });
      }
    }

    return results;
  }

  /**
   * Ultra-fast bulk insert - minimal fields and optimized SQL
   */
  private static async bulkInsertListings(listingsData: CreateListingData[]): Promise<{ count: number }> {
    // Use minimal fields to reduce index overhead
    const insertCount = await db.transaction(async (tx) => {
      const now = new Date().toISOString();
      
      // Prepare minimal data with only required fields
      const values = listingsData.map(data => ({
        workspaceId: data.workspaceId,
        createdBy: data.createdBy,
        businessName: data.businessName,
        industry: data.industry,
        status: this.validateStatus(data.status),
        listingType: 'business_sale',
        teamVisibility: 'all',
        title: data.title || data.businessName,
        createdAt: now,
        updatedAt: now,
      }));

      // Single bulk insert with minimal fields
      await tx.insert(listings).values(values);

      return values.length;
    });

    return { count: insertCount };
  }

  /**
   * Validate and preview CSV data without saving to database
   */
  static async validateCsvData(file: File, workspaceId: string, createdBy: string): Promise<any> {
    // Parse CSV file
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFile(file);

    // If there are validation errors, return them
    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV validation errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }

    const results = {
      preview: [] as any[],
      valid: [] as any[],
      invalid: [] as any[],
      summary: {
        total_rows: parsedData.data.length,
        valid_rows: 0,
        invalid_rows: 0,
        required_fields: ['business_name', 'industry'],
        optional_fields: [
          'asking_price', 'cash_flow_sde', 'annual_revenue', 'status', 
          'general_location', 'year_established', 'employees', 'owner_hours_week',
          'date_listed', 'brief_description', 'reason_for_sale', 'growth_opportunities'
        ]
      }
    };

    // Process each row for validation and preview
    for (let i = 0; i < parsedData.data.length; i++) {
      try {
        const csvRow = parsedData.data[i];
        const listingData = CsvParser.csvRowToListingData(csvRow, workspaceId, createdBy);
        
        // Additional business logic validation
        const validationErrors: string[] = [];
        
        // Validate asking price if provided
        if (listingData.asking_price !== undefined && listingData.asking_price <= 0) {
          validationErrors.push('Asking price must be greater than 0');
        }
        
        // Validate year established
        if (listingData.year_established !== undefined) {
          const currentYear = new Date().getFullYear();
          if (listingData.year_established < 1800 || listingData.year_established > currentYear) {
            validationErrors.push(`Year established must be between 1800 and ${currentYear}`);
          }
        }
        
        // Validate employee count
        if (listingData.employees !== undefined && listingData.employees < 0) {
          validationErrors.push('Number of employees cannot be negative');
        }
        
        // Validate owner hours per week
        if (listingData.owner_hours_week !== undefined && (listingData.owner_hours_week < 0 || listingData.owner_hours_week > 168)) {
          validationErrors.push('Owner hours per week must be between 0 and 168');
        }

        if (validationErrors.length > 0) {
          results.invalid.push({
            row: i + 2, // +2 because index is 0-based and we skip header
            errors: validationErrors,
            data: listingData,
            original_csv_data: csvRow
          });
          results.summary.invalid_rows++;
        } else {
          results.valid.push({
            row: i + 2,
            data: listingData,
            original_csv_data: csvRow
          });
          results.summary.valid_rows++;
        }

        // Add to preview (first 10 rows for display)
        if (results.preview.length < 10) {
          results.preview.push({
            row: i + 2,
            business_name: listingData.business_name,
            industry: listingData.industry,
            asking_price: listingData.asking_price,
            status: listingData.status || 'draft',
            location: listingData.general_location,
            validation_status: validationErrors.length > 0 ? 'invalid' : 'valid',
            errors: validationErrors
          });
        }

      } catch (error) {
        results.invalid.push({
          row: i + 2,
          errors: [error instanceof Error ? error.message : 'Unknown parsing error'],
          data: parsedData.data[i],
          original_csv_data: parsedData.data[i]
        });
        results.summary.invalid_rows++;
      }
    }

    return results;
  }

  /**
   * Bulk create listings from CSV file - ULTRA-HIGH-PERFORMANCE VERSION
   * Optimized for minimal processing time with streamlined operations
   */
  static async bulkCreateListingsFromCsv(file: File, workspaceId: string, createdBy: string): Promise<any> {
    const startTime = Date.now();
    
    // Use streamlined CSV parser for better performance
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFileStreamlined(file);

    // If there are parsing errors, return them
    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV parsing errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }

    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    // Convert all CSV data to listing format in single pass using streamlined converter
    const validListingsData: CreateListingData[] = [];
    
    parsedData.data.forEach((csvRow, index) => {
      try {
        const listingData = CsvParser.csvRowToListingDataStreamlined(csvRow, workspaceId, createdBy);
        validListingsData.push(listingData);
      } catch (error) {
        results.failed.push({
          index,
          error: error instanceof Error ? error.message : 'Data conversion error',
          data: csvRow,
        });
      }
    });

    // Use the optimized bulk creation method
    if (validListingsData.length > 0) {
      const bulkResults = await this.bulkCreateListings(validListingsData);
      results.created.push(...bulkResults.created);
      results.failed.push(...bulkResults.failed);
    }

    const endTime = Date.now();
    console.log(`CSV import completed in ${endTime - startTime}ms for ${parsedData.data.length} rows`);

    return results;
  }

  /**
   * Advanced bulk import with progress tracking and memory optimization
   * Suitable for very large CSV files that need progress reporting
   */
  static async bulkCreateListingsFromCsvWithProgress(
    file: File, 
    workspaceId: string, 
    createdBy: string,
    progressCallback?: (progress: { processed: number; total: number; created: number; failed: number }) => void
  ): Promise<any> {
    // Parse CSV file
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFile(file);

    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV parsing errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }

    const BATCH_SIZE = 100;
    const results = {
      created: [] as any[],
      failed: [] as any[],
    };

    const totalRows = parsedData.data.length;
    let processedRows = 0;

    // Process in batches with progress tracking
    for (let i = 0; i < parsedData.data.length; i += BATCH_SIZE) {
      const batch = parsedData.data.slice(i, i + BATCH_SIZE);
      
      // Convert CSV rows to listing data
      const batchListingsData: CreateListingData[] = [];
      const batchConversionErrors: any[] = [];

      for (let j = 0; j < batch.length; j++) {
        try {
          const csvRow = batch[j];
          const listingData = CsvParser.csvRowToListingData(csvRow, workspaceId, createdBy);
          
          if (!listingData.business_name || !listingData.industry) {
            throw new Error('Missing required fields');
          }
          
          batchListingsData.push(listingData);
        } catch (error) {
          batchConversionErrors.push({
            index: i + j,
            error: error instanceof Error ? error.message : 'Data conversion error',
            data: batch[j],
          });
        }
      }

      // Add conversion errors
      results.failed.push(...batchConversionErrors);

      // Process valid batch data
      if (batchListingsData.length > 0) {
        const batchResult = await this.bulkInsertListings(batchListingsData);
        // Add the count to created results (without detailed records)
        for (let i = 0; i < batchResult.count; i++) {
          results.created.push({ success: true });
        }
      }

      processedRows += batch.length;

      // Report progress if callback provided
      if (progressCallback) {
        progressCallback({
          processed: processedRows,
          total: totalRows,
          created: results.created.length,
          failed: results.failed.length,
        });
      }

      // Yield control to event loop to prevent blocking
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    return results;
  }

  /**
   * Performance monitoring for bulk import operations
   * Provides detailed timing and memory usage statistics
   */
  static async bulkImportWithPerformanceMetrics(
    file: File,
    workspaceId: string,
    createdBy: string
  ): Promise<{
    results: any;
    performance: {
      totalTime: number;
      parseTime: number;
      processTime: number;
      recordsPerSecond: number;
      memoryUsage: {
        heapUsed: number;
        heapTotal: number;
        external: number;
      };
    };
  }> {
    const startTime = Date.now();
    let parseEndTime: number;
    
    // Monitor initial memory usage
    const initialMemory = process.memoryUsage();
    
    console.log(`Starting bulk import for file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
    
    // Parse CSV with timing
    const parseStartTime = Date.now();
    const parsedData: ParsedCsvData = await CsvParser.parseCsvFile(file);
    parseEndTime = Date.now();
    
    console.log(`CSV parsing completed in ${parseEndTime - parseStartTime}ms for ${parsedData.data.length} rows`);
    
    if (parsedData.errors.length > 0) {
      throw new HTTPException(400, {
        message: `CSV parsing errors: ${parsedData.errors.map(e => `Row ${e.row}: ${e.message}`).join('; ')}`
      });
    }
    
    // Process data with timing and progress tracking
    const processStartTime = Date.now();
    
    const results = await this.bulkCreateListingsFromCsvWithProgress(
      file,
      workspaceId,
      createdBy,
      (progress) => {
        const progressPercent = ((progress.processed / progress.total) * 100).toFixed(1);
        console.log(`Import progress: ${progressPercent}% (${progress.processed}/${progress.total}) - Created: ${progress.created}, Failed: ${progress.failed}`);
      }
    );
    
    const processEndTime = Date.now();
    const totalEndTime = Date.now();
    
    // Calculate performance metrics
    const totalTime = totalEndTime - startTime;
    const parseTime = parseEndTime - parseStartTime;
    const processTime = processEndTime - processStartTime;
    const recordsPerSecond = Math.round((results.created.length / (totalTime / 1000)) * 100) / 100;
    
    // Monitor final memory usage
    const finalMemory = process.memoryUsage();
    
    const performanceMetrics = {
      totalTime,
      parseTime,
      processTime,
      recordsPerSecond,
      memoryUsage: {
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        heapTotal: finalMemory.heapTotal,
        external: finalMemory.external,
      },
    };
    
    console.log('Bulk import completed with performance metrics:', {
      ...performanceMetrics,
      created: results.created.length,
      failed: results.failed.length,
      successRate: `${((results.created.length / (results.created.length + results.failed.length)) * 100).toFixed(1)}%`
    });
    
    return {
      results,
      performance: performanceMetrics,
    };
  }

  /**
   * Get listing status history
   */
  static async getListingStatusHistory(listingId: string, workspaceId: string) {
    // Verify listing exists and belongs to workspace
    await this.getListingById(listingId, workspaceId, false);

    const history = await db
      .select({
        id: listingStatusHistory.id,
        fromStatus: listingStatusHistory.fromStatus,
        toStatus: listingStatusHistory.toStatus,
        reason: listingStatusHistory.reason,
        notes: listingStatusHistory.notes,
        changedBy: listingStatusHistory.changedBy,
        createdAt: listingStatusHistory.createdAt,
        changedByName: sql<string>`concat(${userProfiles.firstName}, ' ', ${userProfiles.lastName})`,
      })
      .from(listingStatusHistory)
      .leftJoin(userProfiles, eq(listingStatusHistory.changedBy, userProfiles.userId))
      .where(and(
        eq(listingStatusHistory.listingId, listingId),
        eq(listingStatusHistory.workspaceId, workspaceId)
      ))
      .orderBy(desc(listingStatusHistory.createdAt));

    return history;
  }

  /**
   * Get listings analytics/statistics
   */
  static async getListingStats(workspaceId: string) {
    const [stats] = await db
      .select({
        total: count(),
        active: count(sql`CASE WHEN ${listings.status} = 'active' THEN 1 END`),
        pending: count(sql`CASE WHEN ${listings.status} = 'pending' THEN 1 END`),
        sold: count(sql`CASE WHEN ${listings.status} = 'sold' THEN 1 END`),
        avgAskingPrice: sql<number>`avg(${listings.askingPrice}::numeric)`,
        avgDaysListed: sql<number>`avg(${listings.daysListed})`,
      })
      .from(listings)
      .where(eq(listings.workspaceId, workspaceId));

    return stats;
  }




  // static async AIQuarterlyReport(listingId: string, workspaceId: string): Promise<string> {
  //   try {
      // Get the complete listing data
//       const listing = await this.getListingById(listingId, workspaceId, true);
//       const systemPrompt = `You are a business broker expert. Analyze this business listing and provide insights about market positioning, valuation, opportunities, and recommendations.`;
//       const userInput = `Analyze this business listing:
// Business: ${listing.businessName}
// Industry: ${listing.industry}
// Asking Price: ${listing.askingPrice ? `$${parseFloat(listing.askingPrice).toLocaleString()}` : 'Not specified'}
// Annual Revenue: ${listing.annualRevenue ? `$${parseFloat(listing.annualRevenue).toLocaleString()}` : 'Not specified'}`

//       const aiResponse = await OpenAIService.chatCompletion({
//         systemPrompt,
//         userInput,
//         model: "gpt-4o-mini",
//       });

//       return aiResponse.content;
//     } catch (error) {
//       if (error instanceof Error && error.message.includes('OpenAI service is not available')) {
//         throw new HTTPException(503, { message: "AI features are not available. OpenAI API key is not configured." });
//       }
//       throw error;
    // }
  // }

}